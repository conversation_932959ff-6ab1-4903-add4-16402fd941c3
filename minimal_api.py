#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简化的生产数据API
只包含两个核心接口：插入生产消息和插入故障数据
"""

from flask import Flask, request, jsonify
from datetime import datetime
from production_data_api import ProductionDataAPI

# 创建一个扩展类来支持 line2
class MultiLineAPI(ProductionDataAPI):
    """支持多生产线的API类"""

    def insert_line2_production_message(self, topic: str, counter=None, received_at=None):
        """插入 line2 生产消息"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}

        try:
            cursor = self.connection.cursor()

            if received_at is None:
                received_at = datetime.now()

            # 获取 line2 的 product_id
            cursor.execute("SELECT product_id FROM production_lines WHERE line_number = 2 LIMIT 1")
            result = cursor.fetchone()
            product_id = result[0] if result else None

            # 插入到 line2 表
            query = """
                INSERT INTO line2_production_message_log (topic, counter, product_id, received_at)
                VALUES (%s, %s, %s, %s)
            """

            cursor.execute(query, (topic, counter, product_id, received_at))
            message_id = cursor.lastrowid

            # 如果是计数器消息，调用 line2 处理存储过程
            if topic == 'factory/line2/counter':
                cursor.callproc('process_line2_counter', [topic, received_at])

            cursor.close()

            return {
                "success": True,
                "message_id": message_id,
                "topic": topic,
                "counter": counter,
                "product_id": product_id,
                "received_at": received_at.isoformat(),
                "line": 2
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def insert_line2_malfunction_raw(self, line_number: int, status: str, cmd: int, timestamp=None):
        """插入 line2 故障数据"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}

        # 验证输入参数
        if status not in 'ABCDEFGHIJ':
            return {"success": False, "error": "故障状态类型必须是A-J之间的字符"}

        if cmd not in [0, 1]:
            return {"success": False, "error": "操作命令必须是0或1"}

        try:
            cursor = self.connection.cursor()

            if timestamp is None:
                timestamp = datetime.now()

            # 调用 line2 存储过程
            cursor.callproc('insert_line2_malfunction_raw',
                          [line_number, status, cmd, timestamp])

            # 获取插入的记录ID
            cursor.execute("""
                SELECT id FROM line2_malfunction_raw_data
                WHERE line = %s AND status = %s AND cmd = %s
                ORDER BY id DESC LIMIT 1
            """, (line_number, status, cmd))

            result = cursor.fetchone()
            malfunction_id = result[0] if result else None

            cursor.close()

            return {
                "success": True,
                "malfunction_id": malfunction_id,
                "line_number": line_number,
                "status": status,
                "cmd": cmd,
                "timestamp": timestamp.isoformat(),
                "line": 2
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

app = Flask(__name__)

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'root',
    'password': '20016f7d55f95fb9',  # 请修改为实际密码
    'database': 'production_data'
}

def get_api_instance():
    """获取API实例"""
    api = MultiLineAPI(**DB_CONFIG)
    if api.connect():
        return api
    return None

@app.route('/api/production/message/<int:line_number>', methods=['POST'])
def insert_production_message(line_number):
    """
    插入生产消息

    POST /api/production/message/1  - Line1
    POST /api/production/message/2  - Line2
    {
        "topic": "factory/line1/counter",
        "counter": 1,
        "received_at": "2025-08-23T14:30:00"  // 可选
    }
    """
    # 验证生产线编号
    if line_number not in [1, 2]:
        return jsonify({"success": False, "error": "生产线编号必须是1或2"}), 400

    try:
        data = request.get_json()

        if not data:
            return jsonify({"success": False, "error": "请提供JSON数据"}), 400

        topic = data.get('topic')
        if not topic:
            return jsonify({"success": False, "error": "topic字段是必需的"}), 400

        counter = data.get('counter')
        received_at_str = data.get('received_at')

        # 解析时间
        received_at = None
        if received_at_str:
            try:
                received_at = datetime.fromisoformat(received_at_str.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"success": False, "error": "时间格式错误，请使用ISO格式"}), 400

        # 插入数据
        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500

        try:
            if line_number == 1:
                result = api.insert_production_message(topic, counter, received_at)
            else:  # line_number == 2
                result = api.insert_line2_production_message(topic, counter, received_at)

            # 添加生产线信息到响应
            if result.get('success'):
                result['line_number'] = line_number

            return jsonify(result), 200 if result['success'] else 500
        finally:
            api.disconnect()

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/malfunction/raw/<int:line_number>', methods=['POST'])
def insert_malfunction_raw(line_number):
    """
    插入故障原始数据

    POST /api/malfunction/raw/1  - Line1
    POST /api/malfunction/raw/2  - Line2
    {
        "status": "A",
        "cmd": 1,
        "timestamp": "2025-08-23T14:30:00"  // 可选
    }
    """
    # 验证生产线编号
    if line_number not in [1, 2]:
        return jsonify({"success": False, "error": "生产线编号必须是1或2"}), 400

    try:
        data = request.get_json()

        if not data:
            return jsonify({"success": False, "error": "请提供JSON数据"}), 400

        # 验证必需字段
        status = data.get('status')
        cmd = data.get('cmd')

        if not status:
            return jsonify({"success": False, "error": "status字段是必需的"}), 400
        if cmd is None:
            return jsonify({"success": False, "error": "cmd字段是必需的"}), 400

        timestamp_str = data.get('timestamp')

        # 解析时间
        timestamp = None
        if timestamp_str:
            try:
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"success": False, "error": "时间格式错误，请使用ISO格式"}), 400

        # 插入数据
        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500

        try:
            if line_number == 1:
                result = api.insert_malfunction_raw(line_number, status, cmd, timestamp)
            else:  # line_number == 2
                result = api.insert_line2_malfunction_raw(line_number, status, cmd, timestamp)

            # 添加生产线信息到响应
            if result.get('success'):
                result['line_number'] = line_number

            return jsonify(result), 200 if result['success'] else 500
        finally:
            api.disconnect()

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/product-id/<int:line_number>', methods=['GET'])
def get_product_id(line_number):
    """
    查询指定生产线的 product_id

    GET /api/product-id/1  - 查询Line1的product_id
    GET /api/product-id/2  - 查询Line2的product_id
    """
    # 验证生产线编号
    if line_number not in [1, 2]:
        return jsonify({"success": False, "error": "生产线编号必须是1或2"}), 400

    try:
        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500

        try:
            result = api.get_product_id(line_number)
            return jsonify(result), 200 if result['success'] else 404
        finally:
            api.disconnect()

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/product-id/<int:line_number>', methods=['PUT'])
def update_product_id(line_number):
    """
    更新指定生产线的 product_id

    PUT /api/product-id/1  - 更新Line1的product_id
    PUT /api/product-id/2  - 更新Line2的product_id
    {
        "product_id": "300-596-205"
    }
    """
    # 验证生产线编号
    if line_number not in [1, 2]:
        return jsonify({"success": False, "error": "生产线编号必须是1或2"}), 400

    try:
        data = request.get_json()

        if not data:
            return jsonify({"success": False, "error": "请提供JSON数据"}), 400

        new_product_id = data.get('product_id')
        if not new_product_id:
            return jsonify({"success": False, "error": "product_id字段是必需的"}), 400

        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500

        try:
            result = api.update_product_id(line_number, new_product_id)
            return jsonify(result), 200 if result['success'] else 400
        finally:
            api.disconnect()

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/product-id', methods=['GET'])
def get_all_product_ids():
    """
    查询所有生产线的 product_id

    GET /api/product-id  - 查询所有生产线的product_id
    """
    try:
        api = get_api_instance()
        if not api:
            return jsonify({"success": False, "error": "数据库连接失败"}), 500

        try:
            result = api.get_all_product_ids()
            return jsonify(result), 200 if result['success'] else 500
        finally:
            api.disconnect()

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({"success": False, "error": "接口不存在"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"success": False, "error": "服务器内部错误"}), 500

if __name__ == '__main__':
    print("=== 多生产线数据API ===")
    print("API接口:")
    print("POST /api/production/message/1 - 插入Line1生产消息")
    print("POST /api/malfunction/raw/1 - 插入Line1故障数据")
    print("POST /api/production/message/2 - 插入Line2生产消息")
    print("POST /api/malfunction/raw/2 - 插入Line2故障数据")
    print("")
    print("Product ID 管理接口:")
    print("GET  /api/product-id - 查询所有生产线的product_id")
    print("GET  /api/product-id/1 - 查询Line1的product_id")
    print("GET  /api/product-id/2 - 查询Line2的product_id")
    print("PUT  /api/product-id/1 - 更新Line1的product_id")
    print("PUT  /api/product-id/2 - 更新Line2的product_id")
    print("")
    print("服务器地址: http://localhost:5000")
    print("========================")

    app.run(host='0.0.0.0', port=5000, debug=True)

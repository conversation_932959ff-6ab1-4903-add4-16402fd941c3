#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产数据API基础类
提供数据库连接和基础的数据操作方法
"""

import mysql.connector
from datetime import datetime
from typing import Optional, Dict, Any


class ProductionDataAPI:
    """生产数据API基础类"""
    
    def __init__(self, host: str, port: int, user: str, password: str, database: str):
        """初始化数据库配置"""
        self.config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
    
    def connect(self) -> bool:
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
    
    def insert_production_message(self, topic: str, counter=None, received_at=None):
        """插入 line1 生产消息"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        try:
            cursor = self.connection.cursor()
            
            if received_at is None:
                received_at = datetime.now()
            
            # 获取 line1 的 product_id
            cursor.execute("SELECT product_id FROM production_lines WHERE line_number = 1 LIMIT 1")
            result = cursor.fetchone()
            product_id = result[0] if result else None
            
            # 插入到 line1 表
            query = """
                INSERT INTO line1_production_message_log (topic, counter, product_id, received_at)
                VALUES (%s, %s, %s, %s)
            """
            
            cursor.execute(query, (topic, counter, product_id, received_at))
            message_id = cursor.lastrowid
            
            # 如果是计数器消息，调用 line1 处理存储过程
            if topic == 'factory/line1/counter':
                cursor.callproc('process_line1_counter', [topic, received_at])
            
            cursor.close()
            
            return {
                "success": True,
                "message_id": message_id,
                "topic": topic,
                "counter": counter,
                "product_id": product_id,
                "received_at": received_at.isoformat(),
                "line": 1
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def insert_malfunction_raw(self, line_number: int, status: str, cmd: int, timestamp=None):
        """插入 line1 故障数据"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        # 验证输入参数
        if status not in 'ABCDEFGHIJ':
            return {"success": False, "error": "故障状态类型必须是A-J之间的字符"}
        
        if cmd not in [0, 1]:
            return {"success": False, "error": "操作命令必须是0或1"}
        
        try:
            cursor = self.connection.cursor()
            
            if timestamp is None:
                timestamp = datetime.now()
            
            # 调用 line1 存储过程
            cursor.callproc('insert_line1_malfunction_raw',
                          [line_number, status, cmd, timestamp])
            
            # 获取插入的记录ID
            cursor.execute("""
                SELECT id FROM line1_malfunction_raw_data
                WHERE line = %s AND status = %s AND cmd = %s
                ORDER BY id DESC LIMIT 1
            """, (line_number, status, cmd))
            
            result = cursor.fetchone()
            malfunction_id = result[0] if result else None
            
            cursor.close()
            
            return {
                "success": True,
                "malfunction_id": malfunction_id,
                "line_number": line_number,
                "status": status,
                "cmd": cmd,
                "timestamp": timestamp.isoformat(),
                "line": 1
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_product_id(self, line_number: int) -> Dict[str, Any]:
        """查询指定生产线的 product_id"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        # 验证生产线编号
        if line_number not in [1, 2]:
            return {"success": False, "error": "生产线编号必须是1或2"}
        
        try:
            cursor = self.connection.cursor()
            
            query = """
                SELECT line_id, line_number, product_id, created_at, updated_at
                FROM production_lines
                WHERE line_number = %s
            """
            
            cursor.execute(query, (line_number,))
            result = cursor.fetchone()
            cursor.close()
            
            if result:
                return {
                    "success": True,
                    "line_id": result[0],
                    "line_number": result[1],
                    "product_id": result[2],
                    "created_at": result[3].isoformat() if result[3] else None,
                    "updated_at": result[4].isoformat() if result[4] else None
                }
            else:
                return {"success": False, "error": f"未找到生产线 {line_number} 的信息"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def update_product_id(self, line_number: int, new_product_id: str) -> Dict[str, Any]:
        """更新指定生产线的 product_id"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        # 验证生产线编号
        if line_number not in [1, 2]:
            return {"success": False, "error": "生产线编号必须是1或2"}
        
        # 验证 product_id
        if not new_product_id or not new_product_id.strip():
            return {"success": False, "error": "product_id 不能为空"}
        
        try:
            cursor = self.connection.cursor()
            
            # 检查生产线是否存在
            cursor.execute("SELECT line_id FROM production_lines WHERE line_number = %s", (line_number,))
            if not cursor.fetchone():
                cursor.close()
                return {"success": False, "error": f"生产线 {line_number} 不存在"}
            
            # 更新 product_id
            query = """
                UPDATE production_lines
                SET product_id = %s, updated_at = CURRENT_TIMESTAMP
                WHERE line_number = %s
            """
            
            cursor.execute(query, (new_product_id.strip(), line_number))
            affected_rows = cursor.rowcount
            cursor.close()
            
            if affected_rows > 0:
                return {
                    "success": True,
                    "line_number": line_number,
                    "old_product_id": None,  # 可以在需要时查询旧值
                    "new_product_id": new_product_id.strip(),
                    "updated_at": datetime.now().isoformat()
                }
            else:
                return {"success": False, "error": "更新失败，没有记录被修改"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_all_product_ids(self) -> Dict[str, Any]:
        """查询所有生产线的 product_id"""
        if not self.connection or not self.connection.is_connected():
            return {"success": False, "error": "数据库未连接"}
        
        try:
            cursor = self.connection.cursor()
            
            query = """
                SELECT line_id, line_number, product_id, created_at, updated_at
                FROM production_lines
                ORDER BY line_number
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()
            
            production_lines = []
            for result in results:
                production_lines.append({
                    "line_id": result[0],
                    "line_number": result[1],
                    "product_id": result[2],
                    "created_at": result[3].isoformat() if result[3] else None,
                    "updated_at": result[4].isoformat() if result[4] else None
                })
            
            return {
                "success": True,
                "production_lines": production_lines,
                "total_count": len(production_lines)
            }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
